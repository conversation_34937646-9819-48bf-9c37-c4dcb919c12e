const { google } = require('googleapis');

const oauth2Client = new google.auth.OAuth2(
  '664265010148-eu6uhidd6p0901f6um5sev5vof062plh.apps.googleusercontent.com',
  '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',
  'http://localhost:3000/oauth2callback'
);

// escopos necessários
const SCOPES = [
  'https://www.googleapis.com/auth/siteverification',
  'https://www.googleapis.com/auth/webmasters',
];

// gera URL para login
const url = oauth2Client.generateAuthUrl({
  access_type: 'offline',
  scope: SCOPES,
});

console.log('\n🔗 Acesse este link para autorizar:\n', url);

// servidor temporário p/ capturar token
const http = require('http');
const urlModule = require('url');

http
  .createServer(async (req, res) => {
    const qs = new urlModule.URL(req.url, 'http://localhost:3000').searchParams;
    const code = qs.get('code');
    if (code) {
      const { tokens } = await oauth2Client.getToken(code);
      console.log('\n✅ REFRESH TOKEN:\n', tokens.refresh_token);
      res.end('Autorizado! Pode fechar esta aba.');
      process.exit();
    }
  })
  .listen(3000, () => {
    console.log('\n🚀 Aguardando autorização em http://localhost:3000');
  });
