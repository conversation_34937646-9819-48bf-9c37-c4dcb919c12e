import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import logger from '../utils/logger';
import { ConfigError, ExternalServiceError, NotFoundError, ValidationError } from '../types/errors';

import { DomainService } from './domain.service';
import { Route53Service } from './route53.service';

import { ZohoConfigurationRepository } from '../repositories/zoho-configuration.repository';
import { ZohoConfigurationStatus } from '../models/ZohoConfiguration';

const JOINVIX_API_URL = process.env.JOINVIX_API_URL || '';
const JOINVIX_API_KEY = process.env.JOINVIX_API_KEY || '';
const JOINVIX_API_SECRET = process.env.JOINVIX_API_SECRET || '';
const JOINVIX_CONTACT_ID = process.env.JOINVIX_CONTACT_ID || '';

// Domain order interface
interface DomainOrderParams {
  domain: string;
}

export class JoinvixService {
  private authToken: string | null = null;
  private tokenExpiresAt: Date | null = null;
  private domainService: DomainService;
  private route53Service: Route53Service;
  private zohoConfigRepository: ZohoConfigurationRepository;

  constructor() {
    if (!JOINVIX_API_URL) {
      logger.error('JOINVIX API URL is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX API URL is not set');
    }

    if (!JOINVIX_API_KEY) {
      logger.error('JOINVIX API key is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX API key is not set');
    }

    if (!JOINVIX_API_SECRET) {
      logger.error('JOINVIX API secret is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX API secret is not set');
    }

    if (!JOINVIX_CONTACT_ID) {
      logger.error('JOINVIX contact ID is not set', {}, 'joinvix-service/constructor');
      throw new ConfigError('JOINVIX contact ID is not set');
    }

    this.domainService = new DomainService();
    this.route53Service = new Route53Service();
    this.zohoConfigRepository = new ZohoConfigurationRepository();
  }

  /**
   * Search for domain availability
   * @param domainName Domain name to check (e.g., 'example.com.br')
   * @returns Domain availability information
   */
  async searchDomain(domainName: string): Promise<any> {
    try {
      logger.info('Checking domain availability', { domainName }, 'joinvix-service/search-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      let domainAvailable = false;

      try {
        await this.domainService.searchDomain(domainName);
      } catch (error) {
        if (error instanceof NotFoundError) {
          domainAvailable = true;
        } else {
          logger.error('Error checking domain availability', {
            error: (error as Error).message,
            stack: (error as Error).stack,
            domainName
          }, 'joinvix-service/search-domain');
          throw error;
        }
      }

      logger.info('Domain availability check completed', {
        domainName,
        available: domainAvailable
      }, 'joinvix-service/search-domain');

      return domainAvailable;
    } catch (error) {
      logger.error('Error checking domain availability', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domainName
      }, 'joinvix-service/search-domain');

      if (axios.isAxiosError(error) && error.response) {
        logger.error('JoinVix API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'joinvix-service/search-domain');

        throw new ExternalServiceError(
          `Error checking domain availability: ${error.response.data?.message || error.message}`,
          'joinvix-api'
        );
      }

      throw new ExternalServiceError(`Error checking domain availability: ${(error as Error).message}`, 'joinvix-api');
    }
  }

  /**
   * Get authentication token from JOINVIX API
   * @returns The authentication token
   */
  async getAuthToken(): Promise<string> {
    try {
      logger.debug('Checking if token exists and is valid', {}, 'joinvix-service/get-auth-token');

      if (this.isTokenValid()) {
        logger.debug('Using existing token', {}, 'joinvix-service/get-auth-token');
        return this.authToken as string;
      }

      logger.info('Requesting new auth token from JOINVIX API', {}, 'joinvix-service/get-auth-token');

      const url = `${JOINVIX_API_URL}?service=auth&action=token`;
      const response = await axios.post(url, {
        api_key: JOINVIX_API_KEY,
        api_secret: JOINVIX_API_SECRET
      });

      if (!response.data || !response.data.token) {
        throw new ExternalServiceError('Invalid response from JOINVIX API', 'joinvix-api');
      }

      this.authToken = response.data.token;
      const expiresInSeconds = response.data.expires_in || 3600;
      this.tokenExpiresAt = new Date();
      this.tokenExpiresAt.setSeconds(this.tokenExpiresAt.getSeconds() + expiresInSeconds);

      logger.info('JOINVIX auth token obtained successfully', {
        expiresIn: expiresInSeconds,
        expiresAt: this.tokenExpiresAt
      }, 'joinvix-service/get-auth-token');
      return this.authToken as string;
    } catch (error) {
      logger.error('Error getting JOINVIX auth token', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'joinvix-service/get-auth-token');

      if (axios.isAxiosError(error)) {
        console.log("🚀 ~ JoinvixService ~ getAuthToken ~ error:", error.response?.data)
        if (error.response) {
          logger.error('JOINVIX API error response', {
            status: error.response.status,
            data: error.response.data
          }, 'joinvix-service/get-auth-token');
        }
        throw new ExternalServiceError(`Error communicating with JOINVIX API: ${error.message}`, 'joinvix-api');
      }

      throw new ExternalServiceError(`Error getting JOINVIX auth token: ${(error as Error).message}`, 'joinvix-api');
    }
  }

  /**
   * Check if the current token is valid
   * @returns True if the token exists and has not expired, false otherwise
   */
  isTokenValid(): boolean {
    return !!(this.authToken && this.tokenExpiresAt && this.tokenExpiresAt > new Date());
  }

  /**
   * Create a domain order in JoinVix
   * @param params Domain order parameters
   * @returns Order creation response
   */
  async createOrder(user_id: number, client_id: number, params: DomainOrderParams): Promise<any> {
    try {
      logger.info('Creating domain order', { domain: params.domain }, 'joinvix-service/create-order');

      if (!params.domain) {
        throw new ValidationError('Domain name is required');
      }

      const domain = params.domain.toLowerCase();
      // VER DEPOIS
      // const existingConfig = await this.zohoConfigRepository.getByDomainName(domain); 
      // if (existingConfig && existingConfig.user_id !== user_id) {
      //   throw new ValidationError('Domain already exists');
      // }

      const available = await this.searchDomain(domain);
      if (!available) {
        throw new ValidationError('Domain is not available');
      }

      const domains = await this.route53Service.listRoute53Domains();
      const subId = domains.find(d => d.name === domain)?.id;

      if (subId) {
        throw new ValidationError(`Domain name is not available`);
      }

      const awsCreateHZ = await this.route53Service.createHostedZone(domain);
      if (!awsCreateHZ) {
        throw new ValidationError('Error creating hosted zone');
      }

      const hostedZoneId = awsCreateHZ?.hostedZoneId;
      const nameservers = awsCreateHZ?.nameservers;
      const [ns1, ns2, ns3, ns4] = nameservers || [];

      const newConfigId = await this.zohoConfigRepository.create({
        user_id,
        client_id,
        domain_name: domain,
        hosted_zone_id: hostedZoneId
      });

      if (!newConfigId) {
        throw new ValidationError('Error creating config');
      }


      // Get auth token
      const token = await this.getAuthToken();

      // Create order
      const urlCreateOrder = `${JOINVIX_API_URL}?service=domains&action=order`;
      const responseCreateOrder = await axios.post(urlCreateOrder, {
        domain: domain,
        billingcycle: 'annually',
        domaintype: 'register',
        regperiod: 1,
        nameserver1: ns1,
        nameserver2: ns2,
        nameserver3: ns3,
        nameserver4: ns4,
        register_number: '40.573.276/0001-83',
        paymentmethod: 'mailin'
      }, {
        headers: {
          'X-Authorization': token
        }
      });
      console.log("🚀 ~ JoinvixService ~ createOrder ~ responseCreateOrder:", responseCreateOrder)
      
      if (responseCreateOrder.data?.status !== 'success') {
        throw new ExternalServiceError('Error creating domain order', 'joinvix-api');
      }

      const updatedStatusOrdered = await this.zohoConfigRepository.update(newConfigId, { status: ZohoConfigurationStatus.ORDERED });
      if (!updatedStatusOrdered) {
        throw new ValidationError('Error updating config');
      }

      // Accept order
      const urlAcceptOrder = `${JOINVIX_API_URL}?service=domains&action=accept`;
      const responseAcceptOrder = await axios.post(urlAcceptOrder, {
        order_id: responseCreateOrder.data.data.orderid
      }, {
        headers: {
          'X-Authorization': token
        }
      });

      if (responseAcceptOrder.data?.status !== 'success') {
        throw new ExternalServiceError('Error accepting domain order', 'joinvix-api');
      }

      const updatedStatusAccepted = await this.zohoConfigRepository.update(newConfigId, { status: ZohoConfigurationStatus.ACCEPTED });
      if (!updatedStatusAccepted) {
        throw new ValidationError('Error updating config');
      }  

      logger.info('Domain order created successfully', {
        domain: domain,
        response: responseCreateOrder.data
      }, 'joinvix-service/create-order');

      return responseCreateOrder.data;
    } catch (error) {
      logger.error('Error creating domain order', {
        error: (error as Error).message,
        stack: (error as Error).stack,
        domain: params.domain
      }, 'joinvix-service/create-order');

      if (axios.isAxiosError(error) && error.response) {
        console.log("🚀 ~ JoinvixService ~ createOrder ~ error.response:", error.response.data)
        logger.error('JoinVix API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'joinvix-service/create-order');

        throw new ExternalServiceError(
          `Error creating domain order: ${error.response.data?.message || error.message}`,
          'joinvix-api'
        );
      }

      throw new ExternalServiceError(`Error creating domain order: ${(error as Error).message}`, 'joinvix-api');
    }
  }
}

