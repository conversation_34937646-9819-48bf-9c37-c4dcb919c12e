import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import ZohoToken from '../models/ZohoToken';
import sequelize from '../models/index';
import { DataTypes } from 'sequelize';
import { DatabaseError, NotFoundError } from '../types/errors';

export interface CreateZohoTokenDto {
  access_token: string;
}

export class ZohoTokenRepository {
  constructor() {}

  /**
   * Create a new Zoho token and deactivate any existing active tokens
   * @param createDto Data to create the token
   * @returns The created token
   */
  async create(createDto: CreateZohoTokenDto): Promise<any> {
    logger.debug("Creating Zoho token in repository", {}, 'zoho-token-repo/create');

    // Start a transaction to ensure data consistency
    const transaction = await sequelize.transaction();

    try {
      // Set all existing active tokens to inactive
      await ZohoToken(sequelize, DataTypes).update(
        { is_active: false },
        {
          where: { is_active: true },
          transaction
        }
      );

      // Calculate expiration time (1 hour from now)
      const now = new Date();
      const expiresAt = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour in milliseconds

      // Create the new token
      const zohoToken = await ZohoToken(sequelize, DataTypes).create({
        id: uuidv4(),
        access_token: createDto.access_token,
        expires_at: expiresAt,
        is_active: true
      }, { transaction });

      // Commit the transaction
      await transaction.commit();

      logger.info("Zoho token created successfully", { id: zohoToken.id }, 'zoho-token-repo/create');
      return zohoToken;
    } catch (error) {
      // Rollback the transaction in case of error
      await transaction.rollback();

      logger.error("Error creating Zoho token", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
      }, 'zoho-token-repo/create');

      throw new DatabaseError(`Error creating Zoho token: ${(error as Error).message}`);
    }
  }

  /**
   * Get the currently active token
   * @returns The active token or null if none found
   */
  async getActiveToken(): Promise<any> {
    logger.debug("Getting active Zoho token", {}, 'zoho-token-repo/get-active');
    try {
      const token = await ZohoToken(sequelize, DataTypes).findOne({
        where: { is_active: true },
        order: [['createdAt', 'DESC']] // Get the most recently created active token
      });

      return token;
    } catch (error) {
      logger.error("Error getting active Zoho token", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
      }, 'zoho-token-repo/get-active');

      throw new DatabaseError(`Error getting active Zoho token: ${(error as Error).message}`);
    }
  }

  /**
   * Check if a token is active and not expired
   * @param tokenId The token ID to check
   * @returns Boolean indicating if the token is active and not expired
   */
  async isTokenActive(tokenId: string): Promise<boolean> {
    logger.debug("Checking if Zoho token is active", { tokenId }, 'zoho-token-repo/is-active');
    try {
      const token = await ZohoToken(sequelize, DataTypes).findOne({ where: { id: tokenId } });

      if (!token) {
        logger.warn("Token not found", { tokenId }, 'zoho-token-repo/is-active');
        return false;
      }

      // Check if token is marked as active and not expired
      const isActive = token.is_active && !token.isExpired();

      logger.debug("Token active status", {
        tokenId,
        isActive,
        is_active_flag: token.is_active,
        isExpired: token.isExpired(),
        expiresIn: token.timeUntilExpiration()
      }, 'zoho-token-repo/is-active');

      return isActive;
    } catch (error) {
      logger.error("Error checking if Zoho token is active", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        tokenId
      }, 'zoho-token-repo/is-active');

      throw new DatabaseError(`Error checking if Zoho token is active: ${(error as Error).message}`);
    }
  }

  /**
   * Deactivate a token
   * @param tokenId The token ID to deactivate
   * @returns Boolean indicating success
   */
  async deactivateToken(tokenId: string): Promise<boolean> {
    logger.debug("Deactivating Zoho token", { tokenId }, 'zoho-token-repo/deactivate');
    try {
      const token = await ZohoToken(sequelize, DataTypes).findOne({ where: { id: tokenId } });

      if (!token) {
        throw new NotFoundError(`Zoho token with ID ${tokenId} not found`);
      }

      await ZohoToken(sequelize, DataTypes).update(
        { is_active: false },
        { where: { id: tokenId } }
      );

      logger.info("Zoho token deactivated successfully", { tokenId }, 'zoho-token-repo/deactivate');
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }

      logger.error("Error deactivating Zoho token", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        tokenId
      }, 'zoho-token-repo/deactivate');

      throw new DatabaseError(`Error deactivating Zoho token: ${(error as Error).message}`);
    }
  }
}
