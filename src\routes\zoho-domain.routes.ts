import { Router } from 'express';
import { ZohoDomainController } from '../controllers/zoho-domain.controller';
import { authMiddleware } from '../middleware/authMiddleware';
import { identifyClientMiddleware } from '../middleware/identifyClientMiddleware';

const router = Router();
const zohoDomainController = new ZohoDomainController();

/**
 * @swagger
 * /api/zoho-domain/domains:
 *   post:
 *     summary: Add a domain to a Zoho organization
 *     description: Adds a new domain to the Zoho organization
 *     tags: [Zoho Domain]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - domainName
 *             properties:
 *               domainName:
 *                 type: string
 *                 description: Domain name to add (e.g., example.com)
 *     responses:
 *       201:
 *         description: Domain added successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Domain added successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     HTMLVerificationCode:
 *                       type: string
 *                     verificationStatus:
 *                       type: boolean
 *                     domainName:
 *                       type: string
 *                     createdTime:
 *                       type: number
 *                     CNAMEVerificationCode:
 *                       type: string
 *                     isRegisteredByZoho:
 *                       type: boolean
 *                     primary:
 *                       type: boolean
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/domains', authMiddleware, identifyClientMiddleware, (req, res) => zohoDomainController.addDomainToOrganization(req, res));

/**
 * @swagger
 * /api/zoho-domain/domains:
 *   get:
 *     summary: Fetch all domains in a Zoho organization
 *     description: Retrieves a list of all domains in the Zoho organization
 *     tags: [Zoho Domain]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Domains fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Domains fetched successfully
 *                 data:
 *                   type: object
 *                   properties:
 *                     domainVO:
 *                       type: array
 *                       items:
 *                         type: object
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/domains', authMiddleware, (req, res) => zohoDomainController.fetchAllDomains(req, res));

/**
 * @swagger
 * /api/zoho-domain/domains/{domainName}:
 *   get:
 *     summary: Fetch a specific domain in a Zoho organization
 *     description: Retrieves details of a specific domain in the Zoho organization
 *     tags: [Zoho Domain]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: domainName
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name to fetch (e.g., example.com)
 *     responses:
 *       200:
 *         description: Domain fetched successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Domain fetched successfully
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Domain not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/domains/:domainName', authMiddleware, (req, res) => zohoDomainController.fetchDomain(req, res));

/**
 * @swagger
 * /api/zoho-domain/domains/{domainName}/verify:
 *   post:
 *     summary: Verify a domain in a Zoho organization
 *     description: Verifies a domain in the Zoho organization using the specified verification method
 *     tags: [Zoho Domain]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: domainName
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name to verify (e.g., example.com)
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               verificationMethod:
 *                 type: string
 *                 enum: [TXT, CNAME, HTML]
 *                 default: TXT
 *                 description: Method to use for verification
 *     responses:
 *       200:
 *         description: Domain verification result
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                 data:
 *                   type: object
 *                   properties:
 *                     status:
 *                       type: boolean
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/domains/:domainName/verify', authMiddleware, (req, res) => zohoDomainController.verifyDomain(req, res));

/**
 * @swagger
 * /api/zoho-domain/domains/{domainName}:
 *   delete:
 *     summary: Delete a domain from a Zoho organization
 *     description: Removes a domain from the Zoho organization
 *     tags: [Zoho Domain]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: domainName
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name to delete (e.g., example.com)
 *     responses:
 *       200:
 *         description: Domain deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: Domain deleted successfully
 *                 data:
 *                   type: object
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       404:
 *         description: Domain not found
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.delete('/domains/:domainName', authMiddleware, (req, res) => zohoDomainController.deleteDomain(req, res));

export const zohoDomainRoutes = router;
