import { ZohoDomainService } from '../services/zoho-domain.service';
import logger from '../utils/logger';

/**
 * Example showing how to use the Zoho Domain service
 *
 * This example uses the following environment variables:
 * - ZOHO_CLIENT_ID: Zoho OAuth client ID
 * - ZOHO_CLIENT_SECRET: Zoho OAuth client secret
 * - ZOHO_REFRESH_TOKEN: Zoho OAuth refresh token
 * - ZOHO_ORGANIZATION_ID: Zoho organization ID
 * - ZOHO_SCOPES: Comma-separated list of Zoho API scopes (default: ZohoMail.organization.domains.ALL)
 */
async function runExample() {
  try {
    const zohoDomainService = new ZohoDomainService();

    // Example 1: Fetch all domains in the organization
    console.log('Fetching all domains...');
    const allDomains = await zohoDomainService.fetchAllDomains();
    console.log('All domains:', JSON.stringify(allDomains.data, null, 2));

    // Example 2: Add a new domain to the organization
    console.log('\nAdding a new domain...');
    const newDomain = 'example-domain.com';
    const addResult = await zohoDomainService.addDomainToOrganization(newDomain);
    console.log('Domain added:', JSON.stringify(addResult.data, null, 2));

    // Example 3: Fetch a specific domain
    console.log('\nFetching specific domain...');
    const domainDetails = await zohoDomainService.fetchDomain(newDomain);
    console.log('Domain details:', JSON.stringify(domainDetails.data, null, 2));

    // Example 4: Verify the domain using TXT method
    console.log('\nVerifying domain with TXT method...');
    console.log('Note: Before verifying, you need to add the TXT record to your DNS settings.');
    console.log(`TXT Record: ${domainDetails.data.HTMLVerificationCode}`);

    // Uncomment to actually verify the domain
    // const verifyResult = await zohoDomainService.verifyDomain(newDomain, 'TXT');
    // console.log('Verification result:', JSON.stringify(verifyResult.data, null, 2));

    return {
      allDomains: allDomains.data,
      addedDomain: addResult.data,
      domainDetails: domainDetails.data
    };
  } catch (error) {
    logger.error('Error in Zoho Domain example', {
      error: (error as Error).message,
      stack: (error as Error).stack
    }, 'zoho-domain-example');
    throw error;
  }
}

// Uncomment to run the example
// runExample()
//   .then(result => console.log('Example completed successfully'))
//   .catch(error => console.error('Example failed:', error));

export { runExample };
