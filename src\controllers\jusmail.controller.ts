import { Request, Response } from 'express';
import { JusMailService } from '../services/jusmail.service';
import { AppError, ValidationError, ExternalServiceError } from '../types/errors';
import logger from '../utils/logger';
import { sendErrorResponse, sendSuccessResponse } from '../utils/responseUtils';

export class JusMailController {
  private jusmailService: JusMailService;

  constructor() {
    this.jusmailService = new JusMailService();
  }

  /**
   * Check domain availability
   * @param req Express request object
   * @param res Express response object
   */
  async checkDomainAvailability(req: Request, res: Response): Promise<void> {
    try {
      const domainName = req.query.domain as string;

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      const domain = domainName.toLowerCase();

      logger.info('Checking domain availability', { domain }, 'jusmail/check-availability');

      const result = await this.jusmailService.searchDomain(domain);

      logger.info('Domain availability check completed', {
        domain,
        result: result?.status
      }, 'jusmail/check-availability');

      sendSuccessResponse(res, result, undefined, req.tracingId);
    } catch (error) {
      logger.error('Failed to check domain availability', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack,
        domain: req.query.domain
      }, 'jusmail/check-availability');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error checking domain availability', 'jusmail-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Create a domain order
   * @param req Express request object
   * @param res Express response object
   */
  // async createOrder(req: Request, res: Response): Promise<void> {
  //   try {
  //     const { domain } = req.body;

  //     if (!domain) {
  //       throw new ValidationError('Domain name is required');
  //     }

  //     const user_id = req.user?.id;
  //     const client_id = req.user?.client_id;

  //     if (!user_id || !client_id) {
  //       throw new ValidationError('User ID and client ID are required');
  //     }

  //     logger.info('Creating domain order', { domain, user_id, client_id }, 'jusmail/create-order');

  //     const result = await this.jusmailService.createOrder(Number(user_id), Number(client_id), { domain });

  //     logger.info('Domain order created successfully', {
  //       domain,
  //       orderId: result?.data?.orderid
  //     }, 'jusmail/create-order');

  //     sendSuccessResponse(
  //       res,
  //       result,
  //       `Domain order for ${domain} created successfully. The domain will be available shortly.`,
  //       req.tracingId
  //     );
  //   } catch (error) {
  //     logger.error('Failed to create domain order', {
  //       error: (error as any).message,
  //       code: (error as any).code || 'E404',
  //       stack: (error as any).stack,
  //       domain: req.body.domain
  //     }, 'jusmail/create-order');

  //     // If it's already an AppError, use it directly
  //     if (error instanceof AppError) {
  //       sendErrorResponse(res, error, req.tracingId);
  //       return;
  //     }

  //     // Otherwise, wrap it in an ExternalServiceError
  //     const serviceError = new ExternalServiceError('Error creating domain order', 'jusmail-api');
  //     sendErrorResponse(res, serviceError, req.tracingId);
  //   }
  // }
}
