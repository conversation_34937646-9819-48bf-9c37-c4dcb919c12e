import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import logger from '../utils/logger';
import { ConfigError, ExternalServiceError, ValidationError } from '../types/errors';
import { ZohoTokenRepository } from '../repositories/zoho-token.repository';

// Environment variables
const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID || '';
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET || '';
const ZOHO_REDIRECT_URI = process.env.ZOHO_REDIRECT_URI || '';
const ZOHO_SCOPES = process.env.ZOHO_SCOPES || 'ZohoMail.accounts.READ';
const ZOHO_REFRESH_TOKEN = process.env.ZOHO_REFRESH_TOKEN || '';

export class ZohoAuthService {
  private accessToken: string | null = null;
  private accessTokenExpirationTime: number | null = null;
  private zohoTokenRepository: ZohoTokenRepository;

  constructor() {
    // Validate required environment variables
    if (!ZOHO_CLIENT_ID || !ZOHO_CLIENT_SECRET || !ZOHO_REFRESH_TOKEN) {
      logger.error('Zoho OAuth credentials are not set', {}, 'zoho-auth/constructor');
      throw new ConfigError('Zoho OAuth credentials are not set');
    }

    this.accessToken = null;
    this.accessTokenExpirationTime = null;
    this.zohoTokenRepository = new ZohoTokenRepository();
  }

  /**
   * Generate the OAuth authorization URL
   * @param accessType 'offline' to get refresh token, 'online' for access token only
   * @returns The authorization URL to redirect the user to
   */
  generateAuthUrl(accessType: 'offline' | 'online' = 'offline'): string {
    try {
      logger.debug('Generating Zoho OAuth URL', { scopes: ZOHO_SCOPES, accessType }, 'zoho-auth/generate-auth-url');

      if (!ZOHO_REDIRECT_URI) {
        throw new ValidationError('Zoho redirect URI is not set');
      }

      const authUrl = `https://accounts.zoho.com/oauth/v2/auth?client_id=${ZOHO_CLIENT_ID}&response_type=code&redirect_uri=${encodeURIComponent(ZOHO_REDIRECT_URI)}&scope=${encodeURIComponent(ZOHO_SCOPES)}&access_type=${accessType}&prompt=consent`;

      logger.info('Zoho OAuth URL generated successfully', {}, 'zoho-auth/generate-auth-url');
      return authUrl;
    } catch (error) {
      logger.error('Error generating Zoho OAuth URL', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'zoho-auth/generate-auth-url');

      // Rethrow AppError instances as they are already properly formatted
      if (error instanceof ValidationError) {
        throw error;
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error generating Zoho OAuth URL: ${(error as Error).message}`, 'zoho-api');
    }
  }

  /**
   * Exchange authorization code for access token
   * @param code Authorization code received from Zoho
   * @returns Object containing access_token, refresh_token, etc.
   */
  async getAccessToken(code: string): Promise<{
    access_token: string;
    refresh_token?: string;
    api_domain: string;
    token_type: string;
    expires_in: number;
  }> {
    try {
      logger.debug('Exchanging authorization code for access token', { code }, 'zoho-auth/get-access-token');

      if (!ZOHO_REDIRECT_URI || !ZOHO_CLIENT_ID || !ZOHO_CLIENT_SECRET || !ZOHO_SCOPES || !ZOHO_REFRESH_TOKEN) {
        throw new ValidationError('Zoho OAuth credentials are not set');
      }

      const url = `https://accounts.zoho.com/oauth/v2/token?code=${code}&grant_type=authorization_code&client_id=${ZOHO_CLIENT_ID}&client_secret=${ZOHO_CLIENT_SECRET}&redirect_uri=${encodeURIComponent(ZOHO_REDIRECT_URI)}`;

      const response = await axios.post(url);

      logger.info('Zoho access token obtained successfully', {}, 'zoho-auth/get-access-token');
      return response.data;
    } catch (error) {
      logger.error('Error getting Zoho access token', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'zoho-auth/get-access-token');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-auth/get-access-token');

        throw new ExternalServiceError(
          `Error getting Zoho access token: ${error.response.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error getting Zoho access token: ${(error as Error).message}`, 'zoho-api');
    }
  }

  /**
   * Refresh an access token using a refresh token
   * @returns Object containing new access_token, etc.
   */
  async refreshAccessToken(): Promise<{
    access_token: string;
    api_domain: string;
    token_type: string;
    expires_in: number;
  }> {
    try {
      logger.debug('Refreshing access token', {}, 'zoho-auth/refresh-access-token');

      // First check if we have a cached token in memory
      if (this.accessToken && this.accessToken.length > 0 && this.accessTokenExpirationTime && this.accessTokenExpirationTime > Date.now()) {
        logger.debug('Using cached access token from memory', {}, 'zoho-auth/refresh-access-token');
        return {
          access_token: this.accessToken,
          api_domain: 'mail.zoho.com',
          token_type: 'Bearer',
          expires_in: 3600
        };
      }

      // Check if we have an active token in the database
      const activeToken = await this.zohoTokenRepository.getActiveToken();
      if (activeToken && !activeToken.isExpired()) {
        logger.debug('Using active token from database', { tokenId: activeToken.id }, 'zoho-auth/refresh-access-token');

        // Cache the token in memory
        this.accessToken = activeToken.access_token;
        this.accessTokenExpirationTime = activeToken.expires_at.getTime();

        // Calculate remaining time until expiration
        const expiresIn = activeToken.timeUntilExpiration();

        return {
          access_token: activeToken.access_token,
          api_domain: activeToken.api_domain || 'mail.zoho.com',
          token_type: activeToken.token_type || 'Bearer',
          expires_in: expiresIn
        };
      }

      // If no valid token found, get a new one from Zoho
      logger.debug('No valid token found, requesting new token from Zoho', {}, 'zoho-auth/refresh-access-token');
      const url = `https://accounts.zoho.com/oauth/v2/token?refresh_token=${ZOHO_REFRESH_TOKEN}&grant_type=refresh_token&client_id=${ZOHO_CLIENT_ID}&client_secret=${ZOHO_CLIENT_SECRET}`;

      const response = await axios.post(url);

      // Save the token in memory
      this.accessToken = response.data.access_token;
      this.accessTokenExpirationTime = new Date().getTime() + response.data.expires_in * 1000;

      // Save the token in the database
      await this.zohoTokenRepository.create({
        access_token: response.data.access_token
      });

      logger.info('Zoho access token refreshed successfully', {}, 'zoho-auth/refresh-access-token');
      return response.data;
    } catch (error) {
      logger.error('Error refreshing Zoho access token', {
        error: (error as Error).message,
        stack: (error as Error).stack
      }, 'zoho-auth/refresh-access-token');

      // If it's an Axios error with a response, handle it specifically
      if (axios.isAxiosError(error) && error.response) {
        logger.error('Zoho API error response', {
          status: error.response.status,
          data: error.response.data
        }, 'zoho-auth/refresh-access-token');

        throw new ExternalServiceError(
          `Error refreshing Zoho access token: ${error.response.data?.error || error.message}`,
          'zoho-api'
        );
      }

      // Otherwise, wrap it in an ExternalServiceError
      throw new ExternalServiceError(`Error refreshing Zoho access token: ${(error as Error).message}`, 'zoho-api');
    }
  }
}
