import { Request, Response } from 'express';
import { ZohoAuthService } from '../services/zoho-auth.service';
import { AppError, ValidationError, ExternalServiceError } from '../types/errors';
import logger from '../utils/logger';
import { sendErrorResponse, sendSuccessResponse } from '../utils/responseUtils';

export class ZohoAuthController {
  private zohoAuthService: ZohoAuthService;

  constructor() {
    this.zohoAuthService = new ZohoAuthService();
  }

  /**
   * Initiate OAuth authentication with Zoho
   * @param req Express request object
   * @param res Express response object
   */
  async initiateOAuth(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Initiating Zoho OAuth', {}, 'zoho-auth/initiate-oauth');

      // Get access type from query parameters or use default
      const accessType = (req.query.access_type as 'offline' | 'online') || 'offline';

      // Generate the authorization URL
      const authUrl = this.zohoAuthService.generateAuthUrl(accessType);

      logger.info('Zoho OAuth URL generated', { accessType }, 'zoho-auth/initiate-oauth');
      sendSuccessResponse(res, { authUrl }, 'OAuth URL generated successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to initiate Zoho OAuth', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-auth/initiate-oauth');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error initiating Zoho OAuth', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Handle OAuth callback from Zoho
   * @param req Express request object
   * @param res Express response object
   */
  async handleOAuthCallback(req: Request, res: Response): Promise<void> {
    try {
      const { code, error } = req.query;

      logger.info('Received Zoho OAuth callback', { code: !!code, error }, 'zoho-auth/oauth-callback');

      // Check if there was an error in the callback
      if (error) {
        throw new ValidationError(`OAuth error: ${error}`);
      }

      // Validate the authorization code
      if (!code || typeof code !== 'string') {
        throw new ValidationError('Authorization code is required');
      }

      // Exchange the authorization code for an access token
      const tokenData = await this.zohoAuthService.getAccessToken(code);

      logger.info('Zoho OAuth token obtained', {}, 'zoho-auth/oauth-callback');
      sendSuccessResponse(res, tokenData, 'OAuth token obtained successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to handle Zoho OAuth callback', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-auth/oauth-callback');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error handling Zoho OAuth callback', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Refresh an access token using a refresh token
   * @param req Express request object
   * @param res Express response object
   */
  async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { refresh_token } = req.body;

      logger.info('Refreshing Zoho OAuth token', {}, 'zoho-auth/refresh-token');

      // Validate the refresh token
      if (!refresh_token || typeof refresh_token !== 'string') {
        throw new ValidationError('Refresh token is required');
      }

      // Refresh the access token
      const tokenData = await this.zohoAuthService.refreshAccessToken();

      logger.info('Zoho OAuth token refreshed', {}, 'zoho-auth/refresh-token');
      sendSuccessResponse(res, tokenData, 'OAuth token refreshed successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to refresh Zoho OAuth token', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-auth/refresh-token');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error refreshing Zoho OAuth token', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }
}
