import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import logger from '../utils/logger';
import { AuthError, ValidationError } from '../types/errors';
import { getDecodedJwtFromHeader, JwtPayload } from '../utils/jwt.utils';

declare global {
  namespace Express {
    interface Request {
      user?: JwtPayload;
    }
  }
}

export const authMiddleware = async (req: Request, res: Response, next: NextFunction) => {
  try {
    const token = req.headers.authorization || '';

    const decoded = getDecodedJwtFromHeader(token);

    if (!decoded) {
      throw new ValidationError('User not authenticated');
    }

    req.user = decoded

    logger.debug('User authenticated', { userId: decoded.id, tracingId: req.tracingId }, 'auth/middleware');
    next();
  } catch (error) {
    if (error instanceof jwt.JsonWebTokenError || error instanceof jwt.TokenExpiredError) {
      logger.warn('Invalid or expired token', {
        error: error.message,
        code: 'E200',
        tracingId: req.tracingId
      }, 'auth/middleware');
      return res.status(401).json({
        code: 'E200',
        message: 'Authentication failed: ' + error.message,
        traceId: req.tracingId
      });
    }

    logger.error('Authentication error', {
      error: (error as Error).message,
      code: (error as any).code || 'E200',
      tracingId: req.tracingId,
      stack: (error as Error).stack
    }, 'auth/middleware');

    return res.status((error as AuthError).statusCode || 500).json({
      code: (error as any).code || 'E200',
      message: (error as Error).message || 'Authentication failed',
      traceId: req.tracingId
    });
  }
};

