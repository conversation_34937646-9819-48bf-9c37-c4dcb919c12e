import dotenv from 'dotenv';
dotenv.config();

import axios from 'axios';
import logger from '../utils/logger';
import { ConfigError, ExternalServiceError, ValidationError, NotFoundError } from '../types/errors';
import { ZohoAuthService } from './zoho-auth.service';
import { OpenAIService } from './openai.service';
import { DomainService } from './domain.service';
import { Route53Service } from './route53.service';
import { ZohoConfigurationRepository } from '../repositories/zoho-configuration.repository';
import { ZohoConfigurationStatus } from '../models/ZohoConfiguration';

// Environment variables
const ZOHO_CLIENT_ID = process.env.ZOHO_CLIENT_ID || '';
const ZOHO_CLIENT_SECRET = process.env.ZOHO_CLIENT_SECRET || '';
const ZOHO_ORGANIZATION_ID = process.env.ZOHO_ORGANIZATION_ID || '';
const ZOHO_SCOPES = process.env.ZOHO_SCOPES || 'ZohoMail.organization.domains.ALL';

/**
 * Service for managing Zoho domains
 * Provides functionality to add, fetch, and verify domains in a Zoho organization
 */
export class JusMailService {
    private domainService: DomainService;
    private openaiService: OpenAIService;

    constructor() {
        this.domainService = new DomainService();
        this.openaiService = new OpenAIService();
    }

    async searchDomain(domainName: string): Promise<any> {
        try {
            let domainAvailable = false;

            const name = 'Luan Vieira Advocacia';
            const suggestions = await this.openaiService.generateDomainSuggestions(name);
            console.log("🚀 ~ JusMailService ~ searchDomain ~ suggestions:", suggestions)

            try {
                await this.domainService.searchDomain(domainName);
            } catch (error) {
                if (error instanceof NotFoundError) {
                domainAvailable = true;
                } else {
                logger.error('Error checking domain availability', {
                    error: (error as Error).message,
                    stack: (error as Error).stack,
                    domainName
                }, 'jusmail-service/search-domain');
                throw error;
                }
            }

            logger.info('Domain availability check completed', {
                domainName,
                available: domainAvailable
            }, 'jusmail-service/search-domain');

            return domainAvailable;
        } catch (error) {
            logger.error('Error checking domain availability', {
                error: (error as Error).message,
                stack: (error as Error).stack,
                domainName
            }, 'jusmail-service/search-domain');

            if (axios.isAxiosError(error) && error.response) {
                logger.error('JoinVix API error response', {
                    status: error.response.status,
                    data: error.response.data
                }, 'jusmail-service/search-domain');

                throw new ExternalServiceError(
                `Error checking domain availability: ${error.response.data?.message || error.message}`,
                'jusmail-api'
                );
            }

            throw new ExternalServiceError(`Error checking domain availability: ${(error as Error).message}`, 'jusmail-api');
        }
    }



//   async createOrder(user_id: number, client_id: number, domain: string) {
//         try {
            
//             if (!domain) {
//                 throw new ValidationError('Domain name is required');
//             }

//             let domainAvailable = false;

//             logger.info('Checking domain availability', { domain }, 'jusmail-service/search-domain');

//             try {
//                 await this.domainService.searchDomain(domain);
//             } catch (error) {
//                 if (error instanceof NotFoundError) {
//                     domainAvailable = true;
//                 } else {
//                     logger.error('Error checking domain availability', {
//                     error: (error as Error).message,
//                     stack: (error as Error).stack,
//                     domain
//                     }, 'jusmail-service/search-domain');
//                     throw error;
//                 }
//             }

//             if (!domainAvailable) {
//                 throw new ValidationError('Domain is not available');
//             }

//         } catch (error) {

//         }

//     }


}
