import { Request, Response } from 'express';
import { ZohoDomainService } from '../services/zoho-domain.service';
import { AppError, ValidationError, ExternalServiceError, NotFoundError } from '../types/errors';
import logger from '../utils/logger';
import { sendErrorResponse, sendSuccessResponse } from '../utils/responseUtils';

export class ZohoDomainController {
  private zohoDomainService: ZohoDomainService;

  constructor() {
    this.zohoDomainService = new ZohoDomainService();
  }

  /**
   * Add a domain to a Zoho organization
   * @param req Express request object
   * @param res Express response object
   */
  async addDomainToOrganization(req: Request, res: Response): Promise<void> {
    try {
      const { domainName } = req.body;

      logger.info('Adding domain to Zoho organization', { domainName }, 'zoho-domain/add-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      if (!req.user?.id) {
        throw new ValidationError('User not authenticated');
      }

      if (!req.user?.client_id) {
        throw new ValidationError('Client not authenticated');
      }

      const user_id = req.user?.id;
      const client_id = req.user?.client_id;

      // Call service to add domain
      const result = await this.zohoDomainService.addDomainToOrganization(domainName, Number(user_id), client_id);

      logger.info('Domain added successfully', { domainName }, 'zoho-domain/add-domain');
      sendSuccessResponse(res, result.data, 'Domain added successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to add domain to Zoho organization', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-domain/add-domain');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error adding domain to Zoho organization', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Fetch all domains in a Zoho organization
   * @param req Express request object
   * @param res Express response object
   */
  async fetchAllDomains(req: Request, res: Response): Promise<void> {
    try {
      logger.info('Fetching all domains from Zoho organization', {}, 'zoho-domain/fetch-all-domains');

      // Call service to fetch all domains
      const result = await this.zohoDomainService.fetchAllDomains();

      logger.info('Domains fetched successfully', { count: result.data?.domainVO?.length || 0 }, 'zoho-domain/fetch-all-domains');
      sendSuccessResponse(res, result.data, 'Domains fetched successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to fetch domains from Zoho organization', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-domain/fetch-all-domains');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error fetching domains from Zoho organization', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Fetch a specific domain in a Zoho organization
   * @param req Express request object
   * @param res Express response object
   */
  async fetchDomain(req: Request, res: Response): Promise<void> {
    try {
      const domainName = req.params.domainName || req.query.domainName as string;

      logger.info('Fetching specific domain from Zoho organization', { domainName }, 'zoho-domain/fetch-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Call service to fetch specific domain
      const result = await this.zohoDomainService.fetchDomain(domainName);

      logger.info('Domain fetched successfully', { domainName }, 'zoho-domain/fetch-domain');
      sendSuccessResponse(res, result, 'Domain fetched successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to fetch domain from Zoho organization', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-domain/fetch-domain');

      // If it's a NotFoundError, send a specific response
      if (error instanceof NotFoundError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error fetching domain from Zoho organization', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Verify a domain in a Zoho organization
   * @param req Express request object
   * @param res Express response object
   */
  async verifyDomain(req: Request, res: Response): Promise<void> {
    try {
      const domainName = req.params.domainName || req.body.domainName;
      const verificationMethod = req.body.verificationMethod as 'TXT' | 'CNAME' | 'HTML';

      logger.info('Verifying domain in Zoho organization', { domainName, verificationMethod }, 'zoho-domain/verify-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Call service to verify domain
      const result = await this.zohoDomainService.verifyDomain(domainName, verificationMethod || 'TXT');

      // Check if verification was successful
      if (result.data?.status === true) {
        logger.info('Domain verified successfully', { domainName }, 'zoho-domain/verify-domain');
        sendSuccessResponse(res, result.data, 'Domain verified successfully', req.tracingId);
      } else {
        logger.warn('Domain verification failed', {
          domainName,
          error: result.data?.error,
          message: result.data?.message
        }, 'zoho-domain/verify-domain');

        sendSuccessResponse(res, result.data, 'Domain verification failed', req.tracingId);
      }
    } catch (error) {
      logger.error('Failed to verify domain in Zoho organization', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-domain/verify-domain');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error verifying domain in Zoho organization', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }

  /**
   * Delete a domain from a Zoho organization
   * @param req Express request object
   * @param res Express response object
   */
  async deleteDomain(req: Request, res: Response): Promise<void> {
    try {
      const domainName = req.params.domainName || req.body.domainName;

      logger.info('Deleting domain from Zoho organization', { domainName }, 'zoho-domain/delete-domain');

      if (!domainName) {
        throw new ValidationError('Domain name is required');
      }

      // Call service to delete domain
      const result = await this.zohoDomainService.deleteDomain(domainName);

      logger.info('Domain deleted successfully', { domainName }, 'zoho-domain/delete-domain');
      sendSuccessResponse(res, result.data, 'Domain deleted successfully', req.tracingId);
    } catch (error) {
      logger.error('Failed to delete domain from Zoho organization', {
        error: (error as any).message,
        code: (error as any).code || 'E404',
        stack: (error as any).stack
      }, 'zoho-domain/delete-domain');

      // If it's a NotFoundError, send a specific response
      if (error instanceof NotFoundError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in an ExternalServiceError
      const serviceError = new ExternalServiceError('Error deleting domain from Zoho organization', 'zoho-api');
      sendErrorResponse(res, serviceError, req.tracingId);
    }
  }
}
