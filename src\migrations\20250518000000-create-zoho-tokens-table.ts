import { QueryInterface, DataTypes } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    // Create the zoho_tokens table
    await queryInterface.createTable('zoho_tokens', {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      access_token: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      expires_at: {
        type: DataTypes.DATE,
        allowNull: false
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('zoho_tokens', ['is_active'], {
      name: 'idx_zoho_tokens_is_active'
    });
  },

  down: async (queryInterface: QueryInterface) => {
    // Remove indexes
    await queryInterface.removeIndex('zoho_tokens', 'idx_zoho_tokens_is_active');

    // Drop the table
    await queryInterface.dropTable('zoho_tokens');
  }
};
