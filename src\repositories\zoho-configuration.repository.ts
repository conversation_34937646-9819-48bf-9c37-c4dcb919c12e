import { v4 as uuidv4 } from 'uuid';
import logger from '../utils/logger';
import ZohoConfiguration, { ZohoConfigurationStatus } from '../models/ZohoConfiguration';
import sequelize from '../models/index';
import { DataTypes } from 'sequelize';
import { DatabaseError, NotFoundError } from '../types/errors';

export interface CreateZohoConfigurationDto {
  user_id: number;
  client_id: number;
  domain_name: string;
  hosted_zone_id: string;
  organization_id?: string;
}

export interface UpdateZohoConfigurationDto {
  organization_id?: string;
  hosted_zone_id?: string;
  status?: ZohoConfigurationStatus;
  verification_code?: string;
  verified_at?: Date;
  zoho_domain_data?: any;
  domain_id?: string;
  dkim_id?: string;
  dkim_public_key?: string;
}

export class ZohoConfigurationRepository {
  constructor() {}

  /**
   * Create a new Zoho configuration
   * @param createDto Data to create the configuration
   * @returns The created configuration ID
   */
  async create(createDto: CreateZohoConfigurationDto): Promise<string> {
    logger.debug("Creating Zoho configuration in repository", { domain_name: createDto.domain_name }, 'zoho-config-repo/create');
    try {
      const zohoConfig = await ZohoConfiguration(sequelize, DataTypes).create({
        id: uuidv4(),
        user_id: createDto.user_id,
        client_id: createDto.client_id,
        domain_name: createDto.domain_name,
        organization_id: createDto.organization_id || undefined,
        hosted_zone_id: createDto.hosted_zone_id || undefined,
        status: ZohoConfigurationStatus.CREATED,
      });

      return zohoConfig.id;
    } catch (error) {
      logger.error("Error creating Zoho configuration", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name: createDto.domain_name
      }, 'zoho-config-repo/create');
      throw new DatabaseError(`Error creating Zoho configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Get a Zoho configuration by ID
   * @param id Configuration ID
   * @returns The configuration or null if not found
   */
  async getById(id: string): Promise<any> {
    logger.debug("Getting Zoho configuration by ID", { id }, 'zoho-config-repo/get-by-id');
    try {
      const config = await ZohoConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      return config;
    } catch (error) {
      logger.error("Error getting Zoho configuration by ID", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        id
      }, 'zoho-config-repo/get-by-id');
      throw new DatabaseError(`Error getting Zoho configuration by ID: ${(error as Error).message}`);
    }
  }

  /**
   * Get a Zoho configuration by domain name
   * @param domain_name Domain name
   * @returns The configuration or null if not found
   */
  async getByDomainName(domain_name: string): Promise<any> {
    logger.debug("Getting Zoho configuration by domain name", { domain_name }, 'zoho-config-repo/get-by-domain');
    try {
      const config = await ZohoConfiguration(sequelize, DataTypes).findOne({ where: { domain_name } });
      return config;
    } catch (error) {
      logger.error("Error getting Zoho configuration by domain name", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name
      }, 'zoho-config-repo/get-by-domain');
      throw new DatabaseError(`Error getting Zoho configuration by domain name: ${(error as Error).message}`);
    }
  }

  /**
   * Get all Zoho configurations for a user
   * @param user_id User ID
   * @returns Array of configurations
   */
  async getByUserId(user_id: number): Promise<any[]> {
    logger.debug("Getting Zoho configurations by user ID", { user_id }, 'zoho-config-repo/get-by-user');
    try {
      const configs = await ZohoConfiguration(sequelize, DataTypes).findAll({ where: { user_id } });
      return configs;
    } catch (error) {
      logger.error("Error getting Zoho configurations by user ID", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        user_id
      }, 'zoho-config-repo/get-by-user');
      throw new DatabaseError(`Error getting Zoho configurations by user ID: ${(error as Error).message}`);
    }
  }

  /**
   * Get all Zoho configurations for a client
   * @param client_id Client ID
   * @returns Array of configurations
   */
  async getByClientId(client_id: number): Promise<any[]> {
    logger.debug("Getting Zoho configurations by client ID", { client_id }, 'zoho-config-repo/get-by-client');
    try {
      const configs = await ZohoConfiguration(sequelize, DataTypes).findAll({ where: { client_id } });
      return configs;
    } catch (error) {
      logger.error("Error getting Zoho configurations by client ID", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        client_id
      }, 'zoho-config-repo/get-by-client');
      throw new DatabaseError(`Error getting Zoho configurations by client ID: ${(error as Error).message}`);
    }
  }

  /**
   * Update a Zoho configuration
   * @param id Configuration ID
   * @param updateDto Data to update
   * @returns Number of rows affected
   */
  async update(id: string, updateDto: UpdateZohoConfigurationDto): Promise<number> {
    console.log("🚀 ~ ZohoConfigurationRepository ~ update ~ updateDto:", updateDto)
    logger.debug("Updating Zoho configuration", { id }, 'zoho-config-repo/update');
    try {
      const config = await ZohoConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      
      if (!config) {
        throw new NotFoundError(`Zoho configuration with ID ${id} not found`);
      }
      
      const [affectedRows] = await ZohoConfiguration(sequelize, DataTypes).update(updateDto, { where: { id } });
      return affectedRows;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error updating Zoho configuration", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        id
      }, 'zoho-config-repo/update');
      throw new DatabaseError(`Error updating Zoho configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Update a Zoho configuration by domain name
   * @param domain_name Domain name
   * @param updateDto Data to update
   * @returns Number of rows affected
   */
  async updateByDomainName(domain_name: string, updateDto: UpdateZohoConfigurationDto): Promise<number> {
    logger.debug("Updating Zoho configuration by domain name", { domain_name }, 'zoho-config-repo/update-by-domain');
    try {
      const config = await ZohoConfiguration(sequelize, DataTypes).findOne({ where: { domain_name } });
      
      if (!config) {
        throw new NotFoundError(`Zoho configuration with domain name ${domain_name} not found`);
      }
      
      const [affectedRows] = await ZohoConfiguration(sequelize, DataTypes).update(updateDto, { where: { domain_name } });
      return affectedRows;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error updating Zoho configuration by domain name", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name
      }, 'zoho-config-repo/update-by-domain');
      throw new DatabaseError(`Error updating Zoho configuration by domain name: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a Zoho configuration
   * @param id Configuration ID
   * @returns Boolean indicating success
   */
  async delete(id: string): Promise<boolean> {
    logger.debug("Deleting Zoho configuration", { id }, 'zoho-config-repo/delete');
    try {
      const config = await ZohoConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      
      if (!config) {
        throw new NotFoundError(`Zoho configuration with ID ${id} not found`);
      }
      
      await ZohoConfiguration(sequelize, DataTypes).destroy({ where: { id } });
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error deleting Zoho configuration", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        id
      }, 'zoho-config-repo/delete');
      throw new DatabaseError(`Error deleting Zoho configuration: ${(error as Error).message}`);
    }
  }

  /**
   * Delete a Zoho configuration by domain name
   * @param domain_name Domain name
   * @returns Boolean indicating success
   */
  async deleteByDomainName(domain_name: string): Promise<boolean> {
    logger.debug("Deleting Zoho configuration by domain name", { domain_name }, 'zoho-config-repo/delete-by-domain');
    try {
      const config = await ZohoConfiguration(sequelize, DataTypes).findOne({ where: { domain_name } });
      
      if (!config) {
        throw new NotFoundError(`Zoho configuration with domain name ${domain_name} not found`);
      }
      
      await ZohoConfiguration(sequelize, DataTypes).destroy({ where: { domain_name } });
      return true;
    } catch (error) {
      if (error instanceof NotFoundError) {
        throw error;
      }
      
      logger.error("Error deleting Zoho configuration by domain name", {
        error: (error as Error).message,
        code: (error as any).code || 'E500',
        domain_name
      }, 'zoho-config-repo/delete-by-domain');
      throw new DatabaseError(`Error deleting Zoho configuration by domain name: ${(error as Error).message}`);
    }
  }
}
