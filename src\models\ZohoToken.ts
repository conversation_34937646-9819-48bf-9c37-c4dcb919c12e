import { Model, DataTypes, Sequelize } from 'sequelize';

interface ZohoTokenAttributes {
  id: string;
  access_token: string;
  expires_at: Date;
  is_active: boolean;
  createdAt?: Date;
  updatedAt?: Date;
}

class ZohoToken extends Model<ZohoTokenAttributes> implements ZohoTokenAttributes {
  public id!: string;
  public access_token!: string;
  public expires_at!: Date;
  public is_active!: boolean;
  public readonly createdAt!: Date;
  public readonly updatedAt!: Date;

  // Helper method to check if token is expired
  public isExpired(): boolean {
    return new Date() > this.expires_at;
  }

  // Helper method to calculate time until expiration in seconds
  public timeUntilExpiration(): number {
    const now = new Date();
    const expiresAt = this.expires_at;
    return Math.max(0, Math.floor((expiresAt.getTime() - now.getTime()) / 1000));
  }
}

export default (sequelize: Sequelize, _dataTypes: typeof DataTypes): typeof ZohoToken => {
  ZohoToken.init(
    {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      access_token: {
        type: DataTypes.TEXT,
        allowNull: false
      },
      expires_at: {
        type: DataTypes.DATE,
        allowNull: false
      },
      is_active: {
        type: DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    },
    {
      sequelize,
      modelName: 'ZohoToken',
      tableName: 'zoho_tokens',
      timestamps: true
    }
  );

  return ZohoToken;
};
