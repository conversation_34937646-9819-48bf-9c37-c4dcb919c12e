import logger from '../utils/logger';
import { CreateConfigDto } from '@/types/config';
import JuspageConfiguration, { Palette, Theme } from '../models/JuspageConfiguration';
import JuspageExperimentalConfiguration from '../models/JuspageExperimentalConfiguration';
import sequelize from '../models/index';
import { DataTypes } from 'sequelize';

export class ConfigRepository {
  constructor() {
  }

  async createConfigs(user_id: number, client_id: number, createConfigDto: CreateConfigDto) {
    logger.debug("Creating config in repository", { domain: createConfigDto.domain, name: createConfigDto.name }, 'config-repo/create');
    try {
      const {
        domain,
        domain_type,
        current_step,
        wizard_config,
        name = '',
       } = createConfigDto;
      const juspageConfig = await JuspageConfiguration(sequelize, DataTypes).create({
        id: require('uuid').v4(),
        name,
        user_id,
        client_id,
        domain,
        domain_type,
        current_step,
        wizard_config,
      });

      const juspageExperimentalConfig = await JuspageExperimentalConfiguration(sequelize, DataTypes).create({
        id: require('uuid').v4(),
        config_id: juspageConfig.id
      });

      return juspageConfig.id;
    } catch (error) {
      logger.error("Error creating config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        domain: createConfigDto.domain
      }, 'config-repo/create');
      throw error;
    }

  }

  async getConfigsByDomain(domain: string) {
    logger.debug("Getting config from repository", { domain }, 'config-repo/get');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { domain } });
      return config;
    } catch (error) {
      logger.error("Error getting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        domain
      }, 'config-repo/get');
      throw error;
    }
  }

  async getConfigsById(id: string) {
    logger.debug("Getting config from repository", { id }, 'config-repo/get');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { id } });
      return config;
    } catch (error) {
      logger.error("Error getting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        id
      }, 'config-repo/get');
      throw error;
    }
  }

  async getConfigsByUserId(id: number) {
    logger.debug("Getting config from repository", { user_id: id }, 'config-repo/get-by-user-id');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { user_id: id } });
      return config;
    } catch (error) {
      logger.error("Error getting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        user_id: id
      }, 'config-repo/get-by-user-id');
      throw error;
    }
  }

  async getConfigsByClientId(id: number) {
    logger.debug("Getting config from repository", { client_id: id }, 'config-repo/get-by-client-id');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { client_id: id } });
      return config;
    } catch (error) {
      logger.error("Error getting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        user_id: id
      }, 'config-repo/get-by-client-id');
      throw error;
    }
  }

  async setConfigs(id: string, configs: any, is_published: boolean = false, is_public: boolean = true) {
    logger.debug("Setting config in repository", { id }, 'config-repo/set');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).update({
        name: configs.name,
        palette: configs.palette,
        theme: configs.theme,
        domain: configs.domain,
        site_config: configs.config || configs.site_config,
        current_step: configs.current_step,
        wizard_config: configs.wizard_config,
        is_published: is_published,
        is_public: is_public
      }, { where: { id } });
      return config;
    } catch (error) {
      logger.error("Error setting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        domain: id
      }, 'config-repo/set');
      throw error;
    }
  }

  async getExperimentalConfigs(config_id: string) {
    logger.debug("Getting config from repository", { config_id }, 'config-repo/get-experimental');
    try {
      const config = await JuspageExperimentalConfiguration(sequelize, DataTypes).findOne({ where: { config_id } });
      return config;
    } catch (error) {
      logger.error("Error getting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        config_id
      }, 'config-repo/get-experimental');
      throw error;
    }
  }

  async getExperimentalConfigsByUserId(user_id: number) {
    logger.debug("Getting config from repository", { user_id }, 'config-repo/experimental-get-by-user-id');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { user_id: user_id } });
      if (!config) return null;
      const experimental = await JuspageExperimentalConfiguration(sequelize, DataTypes).findOne({ where: { config_id: config?.id } });
      return experimental;
    } catch (error) {
      logger.error("Error getting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        user_id
      }, 'config-repo/experimental-get-by-user-id');
      throw error;
    }
  }

  async setExperimentalConfigs(config_id: string, configs: any) {
    logger.debug("Setting config in repository", { config_id }, 'config-repo/set-experimental');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).update({
        is_published: false
      }, { where: { id: config_id } });
      if (!config) {
        throw new Error(`Config not found config for ${config_id}`);
      };
      const experimental = await JuspageExperimentalConfiguration(sequelize, DataTypes).update({
        palette: configs.palette,
        theme: configs.theme,
        site_config: configs.config || configs.site_config
      }, { where: { config_id } });
      return experimental;
    } catch (error) {
      logger.error("Error setting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        config_id
      }, 'config-repo/set-experimental');
      throw error;
    }
  }

  async deleteConfigs(user_id: number, client_id: number) {
    logger.debug("Deleting config from repository", { user_id, client_id }, 'config-repo/delete');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { user_id, client_id } });
      const deleteExperimental = await JuspageExperimentalConfiguration(sequelize, DataTypes).destroy({ where: { config_id: config?.id } });
      const deleteConfig = await JuspageConfiguration(sequelize, DataTypes).destroy({ where: { id: config?.id } });

      return true;
    } catch (error) {
      logger.error("Error deleting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
      }, 'config-repo/delete');
      throw error;
    }

  }

  async setLogoURL(configId: string, url_logo: string) {
    logger.debug("Setting config in repository", { configId }, 'config-repo/set-logo-url');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { id: configId } });
      await JuspageConfiguration(sequelize, DataTypes).update({
        url_logo
      }, { where: { id: config?.id } });
      const exp = await JuspageExperimentalConfiguration(sequelize, DataTypes).update({
        url_logo
      }, { where: { config_id: config?.id } });
      return config;
    } catch (error) {
      logger.error("Error setting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-repo/set-logo-url');
      throw error;
    }
  }

  async setStep(configId: string, current_step: string) {
    logger.debug("Setting config in repository", { configId }, 'config-repo/set-step');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { id: configId } });
      if (config?.current_step === 'FINISHED' || config?.current_step === current_step) return config;
      await JuspageConfiguration(sequelize, DataTypes).update({
        current_step
      }, { where: { id: config?.id } });
      return config;
    } catch (error) {
      logger.error("Error setting config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-repo/set-step');
      throw error;
    }
  }

  async updateWizardConfig(configId: string, additionalConfig: any) {
    logger.debug("Updating wizard config in repository", { configId }, 'config-repo/update-wizard-config');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { id: configId } });
      if (!config) {
        throw new Error(`Config not found config for ${configId}`);
      }
      const teste = { ...config.wizard_config, ...additionalConfig }
      await JuspageConfiguration(sequelize, DataTypes).update({
        wizard_config: { ...config.wizard_config, ...additionalConfig }
      }, { where: { id: configId } });
      return config;
    } catch (error) {
      logger.error("Error updating wizard config", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-repo/update-wizard-config');
      throw error;
    }
  }

  async setPublicStatus(configId: string, isPublic: boolean) {
    logger.debug("Setting public status in repository", { configId, isPublic }, 'config-repo/set-public-status');
    try {
      const config = await JuspageConfiguration(sequelize, DataTypes).findOne({ where: { id: configId } });
      if (!config) {
        throw new Error(`Config not found for ${configId}`);
      }
      await JuspageConfiguration(sequelize, DataTypes).update({
        is_public: isPublic
      }, { where: { id: configId } });
      return config;
    } catch (error) {
      logger.error("Error setting public status", {
        error: (error as Error).message,
        code: (error as any).code || 'E600',
        configId
      }, 'config-repo/set-public-status');
      throw error;
    }
  }

}
