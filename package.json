{"name": "juspage-backend", "version": "1.0.0", "description": "TypeScript Express API with clean architecture", "main": "dist/index.js", "scripts": {"start": "node dist/index.js", "dev": "nodemon --ignore src/configs/ src/index.ts", "build": "tsc", "test": "jest"}, "dependencies": {"@aws-sdk/client-cloudfront": "^3.782.0", "@aws-sdk/client-route-53": "^3.782.0", "@aws-sdk/client-s3": "^3.782.0", "@aws-sdk/client-sts": "^3.782.0", "@aws-sdk/credential-provider-ini": "^3.782.0", "@google-cloud/aiplatform": "^4.1.0", "@google-cloud/vertexai": "^1.10.0", "@types/jsonwebtoken": "^9.0.9", "@types/multer": "^1.4.12", "@types/uuid": "^10.0.0", "ajv": "^8.17.1", "ajv-formats": "^3.0.1", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "json-server": "^1.0.0-beta.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.2", "multer-s3": "^3.0.1", "openai": "^4.97.0", "pg": "^8.14.1", "pg-hstore": "^2.3.4", "samlify": "^2.10.0", "sequelize": "^6.37.7", "sequelize-cli": "^6.6.2", "sequelize-typescript": "^2.1.6", "sharp": "^0.34.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0", "winston": "^3.17.0", "xml-crypto": "^6.1.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.11", "@types/json-server": "^0.14.8", "@types/node": "^20.17.30", "@types/sequelize": "^4.28.20", "@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-jest": "^29.1.1", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}