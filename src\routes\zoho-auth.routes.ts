import { Router } from 'express';
import { ZohoAuthController } from '../controllers/zoho-auth.controller';
import { authMiddleware } from '../middleware/authMiddleware';

const router = Router();
const zohoAuthController = new ZohoAuthController();

/**
 * @swagger
 * /api/zoho-auth/oauth:
 *   get:
 *     summary: Initiate OAuth authentication with Zoho
 *     description: Generate an authorization URL for Zoho OAuth
 *     tags: [Zoho Auth]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: access_type
 *         required: false
 *         schema:
 *           type: string
 *           enum: [offline, online]
 *         description: Type of access to request (default is offline)
 *     responses:
 *       200:
 *         description: Authorization URL generated successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     authUrl:
 *                       type: string
 *                       description: The authorization URL to redirect the user to
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/oauth', authMiddleware, (req, res) => zohoAuthController.initiateOAuth(req, res));

/**
 * @swagger
 * /api/zoho-auth/oauth/callback:
 *   get:
 *     summary: Handle OAuth callback from Zoho
 *     description: Process the callback from Zoho after user authorization
 *     tags: [Zoho Auth]
 *     parameters:
 *       - in: query
 *         name: code
 *         required: true
 *         schema:
 *           type: string
 *         description: Authorization code from Zoho
 *       - in: query
 *         name: error
 *         required: false
 *         schema:
 *           type: string
 *         description: Error message if authorization failed
 *     responses:
 *       200:
 *         description: OAuth token obtained successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     access_token:
 *                       type: string
 *                       description: The access token
 *                     refresh_token:
 *                       type: string
 *                       description: The refresh token (only with offline access)
 *                     api_domain:
 *                       type: string
 *                       description: The API domain to use
 *                     token_type:
 *                       type: string
 *                       description: The token type (usually "Bearer")
 *                     expires_in:
 *                       type: number
 *                       description: Token expiration time in seconds
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/oauth/callback', (req, res) => zohoAuthController.handleOAuthCallback(req, res));

/**
 * @swagger
 * /api/zoho-auth/oauth/refresh:
 *   post:
 *     summary: Refresh an access token
 *     description: Refresh an access token using a refresh token
 *     tags: [Zoho Auth]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               refresh_token:
 *                 type: string
 *                 description: The refresh token to use
 *     responses:
 *       200:
 *         description: OAuth token refreshed successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 data:
 *                   type: object
 *                   properties:
 *                     access_token:
 *                       type: string
 *                       description: The new access token
 *                     api_domain:
 *                       type: string
 *                       description: The API domain to use
 *                     token_type:
 *                       type: string
 *                       description: The token type (usually "Bearer")
 *                     expires_in:
 *                       type: number
 *                       description: Token expiration time in seconds
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.post('/oauth/refresh', authMiddleware, (req, res) => zohoAuthController.refreshToken(req, res));

export const zohoAuthRoutes = router;
