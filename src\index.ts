import express from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import swaggerUi from 'swagger-ui-express';

import { domainRoutes } from './routes/domain.routes';
import { configRoutes } from './routes/config.routes';
import { statisticsRoutes } from './routes/statistics.routes';
import { sitemapRoutes } from './routes/sitemap.routes';
import { zohoAuthRoutes } from './routes/zoho-auth.routes';
import { zohoDomainRoutes } from './routes/zoho-domain.routes';
import { jusmailRoutes } from './routes/jusmail.routes';
import { requestLogger } from './middleware/requestLogger';
import logger, { setTracingId } from './utils/logger';
import { specs } from './config/swagger';
import { sendErrorResponse } from './utils/responseUtils';

dotenv.config();

const app = express();
const port = process.env.PORT || 4444;

// Middleware
app.use(cors({
  origin: '*', // Allow all origins
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['Content-Range', 'X-Content-Range'],
  maxAge: 86400 // 24 hours
}));
app.use(express.json({ limit: '50mb' }));
app.use(express.urlencoded({ extended: true })); // Para processar dados de formulário
app.use(requestLogger);

// Swagger documentation
app.use('/api-docs', swaggerUi.serve, swaggerUi.setup(specs, { explorer: true }));

// Routes
app.use('/api/domain', domainRoutes);
app.use('/api/config', configRoutes);
app.use('/api/statistics', statisticsRoutes);
app.use('/api/zoho-auth', zohoAuthRoutes);
app.use('/api/zoho-domain', zohoDomainRoutes);
app.use('/api/jusmail', jusmailRoutes);
app.use('/api', sitemapRoutes);

app.use((err: Error, req: express.Request, res: express.Response, _next: express.NextFunction) => {
  const errorCode = (err as any).code || 'E100';

  if (req.tracingId) {
    setTracingId(req.tracingId);
  }

  if ((err as any).getErrorDetails) {
    const details = (err as any).getErrorDetails();
    logger.error(`Unhandled error: ${err.message}`, details, 'error/unhandled');
  } else {
    logger.error(`Unhandled error: ${err.message}`, {
      code: errorCode,
      error: err.message,
      stack: err.stack,
      path: req.path,
      method: req.method
    }, 'error/unhandled');
  }

  // Use the standardized error response utility
  sendErrorResponse(res, err, req.tracingId);
});

app.listen(port, () => {
  logger.info(`Server is running on port ${port}`);
});