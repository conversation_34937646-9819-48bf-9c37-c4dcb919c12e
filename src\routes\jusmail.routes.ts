import { Router } from 'express';
import { JusMailController } from '../controllers/jusmail.controller';
import { authMiddleware } from '../middleware/authMiddleware';
import { identifyClientMiddleware } from '../middleware/identifyClientMiddleware';

const router = Router();
const jusmailController = new JusMailController();

/**
 * @swagger
 * /api/jusmail/check-availability:
 *   get:
 *     summary: Check domain availability
 *     description: Check if a domain is available for registration
 *     tags: [JusMail]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: domain
 *         required: true
 *         schema:
 *           type: string
 *         description: Domain name to check (e.g., example.com.br)
 *     responses:
 *       200:
 *         description: Domain availability information
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   description: Status of the request
 *                 available:
 *                   type: boolean
 *                   description: Whether the domain is available
 *                 domain:
 *                   type: string
 *                   description: The domain name checked
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
router.get('/check-availability', authMiddleware, (req, res) =>
  jusmailController.checkDomainAvailability(req, res)
);

/**
 * @swagger
 * /api/jusmail:
 *   post:
 *     summary: Create a domain order
 *     description: Create a new domain order in JoinVix
 *     tags: [JusMail]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - domain
 *             properties:
 *               domain:
 *                 type: string
 *                 description: Domain name to order (e.g., example.com.br)
 *     responses:
 *       200:
 *         description: Domain order created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: string
 *                   description: Status of the request
 *                 data:
 *                   type: object
 *                   properties:
 *                     orderid:
 *                       type: string
 *                       description: The order ID
 *       400:
 *         description: Bad request
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: Unauthorized
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */
// router.post('/create', authMiddleware, identifyClientMiddleware, (req, res) =>
//   jusmailController.createOrder(req, res)
// );

export const jusmailRoutes = router;
