import logger from '../utils/logger';
import { Request, Response } from 'express';
import { ConfigService } from '../services/config.service';
import { ValidationError, ConfigError, AppError, ExternalServiceError } from '../types/errors';
import { sendErrorResponse, sendSuccessResponse } from '../utils/responseUtils';
import { GeminiService } from '../services/gemini.service';
import { ConfigRepository } from '../repositories/config.repository';
import { DomainType, Palette } from '../models/JuspageConfiguration';
import { FreepikService } from '../services/freepik.service';
import { OpenAIService } from '../services/openai.service';
import { StatisticsService } from '../services/statistics.service';

const DOMAIN_NAME = process.env.DOMAIN_NAME || '';
const DOMAIN_NAME_TEST = process.env.DOMAIN_NAME_TEST || '';

export class ConfigController {
  private configService: ConfigService;
  private geminiService: GeminiService;
  private openaiService: OpenAIService;
  private configRepository: ConfigRepository;
  private freepikService: FreepikService;
  private statisticsService: StatisticsService;

  constructor() {
    this.configService = new ConfigService();
    this.geminiService = new GeminiService();
    this.openaiService = new OpenAIService();
    this.configRepository = new ConfigRepository();
    this.freepikService = new FreepikService();
    this.statisticsService = new StatisticsService();
  }

  async getConfigsByDomain(req: Request, res: Response): Promise<void> {
    try {
      const DOMAIN = req.query.domain as string;

      if (!DOMAIN) {
        if (req.user?.id) {
          const result = await this.configService.getConfigsByUserId(Number(req.user?.id));
          logger.info("Config retrieved successfully by user ID", { userId: req.user?.id }, 'config/get-by-user');
          sendSuccessResponse(res, result, undefined, req.tracingId);
          return;
        }

        throw new ValidationError('Domain name is required');
      }

      const domain = DOMAIN.toLowerCase();

      if (!`${domain}.`.endsWith(DOMAIN_NAME) && !`${domain}.`.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      const result = await this.configService.getConfigsByDomain(domain);
      logger.info("Config retrieved successfully by domain", { domain }, 'config/get-by-domain');
      sendSuccessResponse(res, result, undefined, req.tracingId);
    } catch (error) {
      logger.error("Failed to get config", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        domain: req.query.domain
      }, 'config/get');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async getConfigsExperimental(req: Request, res: Response): Promise<void> {
    try {
      const DOMAIN = req.query.domain as string;

      if (!DOMAIN) {
        if (req.user?.id) {
          const result = await this.configService.getExperimentalConfigsByUserId(Number(req.user?.id));
          logger.info("Experimental config retrieved successfully by user ID", { userId: req.user?.id }, 'config/get-experimental-by-user');
          sendSuccessResponse(res, result, undefined, req.tracingId);
          return;
        }

        throw new ValidationError('Domain name is required');
      }

      const domain = DOMAIN.toLowerCase();

      if (!`${domain}.`.endsWith(DOMAIN_NAME) && !`${domain}.`.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      const result = await this.configService.getConfigsByDomain(domain);
      logger.info("Experimental config retrieved successfully by domain", { domain }, 'config/get-experimental-by-domain');
      sendSuccessResponse(res, result, undefined, req.tracingId);
    } catch (error) {
      logger.error("Failed to get experimental config", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        domain: req.query.domain
      }, 'config/get-experimental');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async uploadLogo(req: Request, res: Response): Promise<void> {
    try {
      if (!req.file){
        throw new ValidationError('Logo image is required');
      }

      let configId
      const config = await this.configService.getConfigsByUserId(Number(req.user?.id));
      configId = config?.id
      if (!config || !config.id) {
        // Check if there are available slots before creating a new config
        // await this.statisticsService.checkSlotsAvailability(Number(req.user?.id));

        const createConfigDto = {
          domain: '',
          domain_type: DomainType.SUBDOMAIN,
          current_step: '1',
          wizard_config: req.body.wizard_config ? JSON.parse(req.body.wizard_config) : {}
        };
        const id = await this.configRepository.createConfigs(Number(req.user?.id), Number(req.user?.client_id), createConfigDto);
        configId = id
      }
      logger.debug("Uploading logo for user", { userId: req.user?.id, configId }, 'config/upload-logo');

      const url = await this.configService.uploadLogo(configId, req.file, req.body.ai === 'true');

      logger.info("Logo uploaded successfully", { configId }, 'config/upload-logo');
      sendSuccessResponse(res, { url }, 'Logo uploaded successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to upload logo", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id
      }, 'config/upload-logo');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async removeLogo(req: Request, res: Response): Promise<void> {
    try {
      logger.debug("Removing logo for user", { userId: req.user?.id }, 'config/remove-logo');

      await this.configService.removeLogo(Number(req.user?.id));

      logger.info("Logo removed successfully", { userId: req.user?.id }, 'config/remove-logo');
      sendSuccessResponse(res, {}, 'Logo removed successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to remove logo", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id
      }, 'config/remove-logo');

      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async updateStep(req: Request, res: Response): Promise<void> {
    try {
      const { step, config } = req.body;
      logger.debug("Updating step for user", { userId: req.user?.id, step }, 'config/update-step');

      if (step !== '2' && step !== '3') {
        throw new ValidationError('Step must be either 2 or 3');
      }

      await this.configService.updateStep(Number(req.user?.id), step, config);

      logger.info("Step updated successfully", { userId: req.user?.id, step }, 'config/update-step');
      sendSuccessResponse(res, { step }, 'Step updated successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to update step", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id,
        step: req.body?.step
      }, 'config/update-step');

      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async setExperimentalConfigs(req: Request, res: Response): Promise<void> {
    try {
      const DOMAIN = req.query.domain as string;

      if (!DOMAIN) {
        throw new ValidationError('Domain name is required');
      }

      const domain = DOMAIN.toLowerCase();

      if (!`${domain}.`.endsWith(DOMAIN_NAME) && !`${domain}.`.endsWith(DOMAIN_NAME_TEST)) {
        throw new ValidationError(`Domain must end with ${DOMAIN_NAME} or ${DOMAIN_NAME_TEST}`);
      }

      logger.debug("User authenticated for experimental config update", { userId: req.user?.id, domain }, 'config/set-experimental');

      await this.configService.setExperimentalConfigs(domain, req.body);

      logger.info("Experimental configs set successfully", { domain }, 'config/set-experimental');
      sendSuccessResponse(res, { domain }, 'Experimental configs set successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to set experimental config", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        domain: req.query.domain
      }, 'config/set-experimental');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async publishExperimentalConfigs(req: Request, res: Response): Promise<void> {
    try {
      logger.debug("Publishing experimental configs for user", { userId: req.user?.id }, 'config/publish-experimental');

      const config = await this.configService.getExperimentalConfigsByUserId(Number(req.user?.id));
      logger.info("Experimental config retrieved successfully", { domain: config.domain }, 'config/publish-experimental');

      const result = await this.configService.publishExperimentalConfigs(config.config.id);
      logger.info("Experimental config published successfully", { domain: config.domain }, 'config/publish-experimental');
      sendSuccessResponse(res, result, 'Experimental configs published successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to publish experimental configs", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id
      }, 'config/publish-experimental');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async resetExperimentalConfigs(req: Request, res: Response): Promise<void> {
    try {
      logger.debug("Resetting experimental configs for user", { userId: req.user?.id }, 'config/reset-experimental');

      const config = await this.configService.getExperimentalConfigsByUserId(Number(req.user?.id));
      if (!config) {
        throw new ConfigError(`Config not found for user ${req.user?.id}`);
      }
      const id = config.config.id;
      logger.info("Experimental config retrieved successfully", { id }, 'config/reset-experimental');

      const result = await this.configService.resetExperimentalConfigs(id);
      logger.info("Experimental config reset successfully", { id }, 'config/reset-experimental');
      sendSuccessResponse(res, result, 'Experimental configs reset successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to reset experimental configs", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id
      }, 'config/reset-experimental');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async getAreasDescription(req: Request, res: Response): Promise<void> {
    try {
      const { areas, experience } = req.query;

      if (!areas) {
        throw new ValidationError('Areas are required');
      }

      const result = await this.openaiService.generateTextFromAreas(String(areas), String(experience));
      logger.info("Areas text generated successfully", { areas }, 'config/generate-areas-description');
      sendSuccessResponse(res, result, 'area description generated successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to generate area description", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        areas: req.body?.areas,
        experience: req.body?.experience
      }, 'config/areas-description');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async generateLogoOptions(req: Request, res: Response): Promise<void> {
    try {
      const { name } = req.query;

      if (!name) {
        throw new ValidationError('Name is required');
      }

      const result = await this.openaiService.generateImageWithOpenAI(String(name));
      logger.info("Logo options generated successfully", { name }, 'config/generate-logo-options');
      sendSuccessResponse(res, result, 'Logo options generated successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to generate logo options", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        name: req.body?.name
      }, 'config/generate-logo-options');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async generateAndSetLogo(req: Request, res: Response): Promise<void> {
    try {
      const { name } = req.query;

      if (!name) {
        throw new ValidationError('Name is required');
      }

      const result = await this.openaiService.generateImageWithOpenAI(String(name));
      const logoBase64 = result[0];

      if (!logoBase64) {
        throw new ExternalServiceError('OpenAI did not return any valid images', 'openai');
      }

      const config = await this.configService.getConfigsByUserId(Number(req.user?.id));

      const url = await this.configService.uploadBase64Logo(config.id, logoBase64, `ai-${new Date().toISOString()}_${config.id}-logo.png`);

      await this.configService.updateLogoURL(config.id, url);

      logger.info("Logo generated and set successfully", { name }, 'config/generate-and-set-logo');
      sendSuccessResponse(res, { logoUrl: url }, 'Logo generated and set successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to generate and set logo", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        name: req.body?.name
      }, 'config/generate-and-set-logo');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async getImageOptions(req: Request, res: Response): Promise<void> {
    try {
      const { term, page, limit, orientation } = req.query;

      if (!term) {
        throw new ValidationError('Term is required');
      }

      const result = await this.freepikService.getImageOptions(String(term), Number(page), Number(limit), orientation as string);
      sendSuccessResponse(res, result, 'Images generated successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to generate images", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        name: req.body?.name
      }, 'config/get-image-options');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async setImage(req: Request, res: Response): Promise<void> {
    try {
      const { imageUrl, placement } = req.body;
      if (!imageUrl) {
        throw new ValidationError('Image URL is required');
      }

      if (!placement) {
        throw new ValidationError('Placement is required');
      }

      const result = await this.configService.getConfigsByUserId(Number(req.user?.id));

      if (!result || !result.id) {
        throw new ValidationError('Config not found');
      }

      await this.configService.setImage(result.id, imageUrl, placement);

      sendSuccessResponse(res, imageUrl, 'Image set successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to set image", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        name: req.body?.name
      }, 'config/set-image');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async createConfig(req: Request, res: Response): Promise<void> {
    try {
      const { name } = req.body;

      if (!name) {
        throw new ValidationError('Name is required');
      }

      // Check if user already has a config
      const existingConfig = await this.configService.getConfigsByUserId(Number(req.user?.id));

      if (existingConfig) {
        // If config exists, update the name
        await this.configRepository.setConfigs(existingConfig.id, {
          ...existingConfig,
          name,
          is_published: false,
          is_public: false
        });

        logger.info("Config name updated successfully", { id: existingConfig.id, name }, 'config/update-name');

        sendSuccessResponse(
          res,
          { id: existingConfig.id },
          'Configuration name updated successfully',
          req.tracingId
        );
        return;
      }

      // Check if there are available slots before creating a new config
      // await this.statisticsService.checkSlotsAvailability(Number(req.user?.id));

      // Create a new config with just the name
      const createConfigDto = {
        name,
        domain: '',
        domain_type: DomainType.SUBDOMAIN,
        current_step: '1',
        wizard_config: {}
      };

      const id = await this.configRepository.createConfigs(
        Number(req.user?.id),
        Number(req.user?.client_id),
        createConfigDto
      );

      logger.info("Config created successfully", { id, name }, 'config/create');

      // Set status code to 201 Created and send success response
      res.status(201);
      sendSuccessResponse(
        res,
        { id },
        'Configuration created successfully',
        req.tracingId
      );
    } catch (error) {
      logger.error("Failed to create config", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        name: req.body?.name
      }, 'config/create');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

  async setConfigPrivate(req: Request, res: Response): Promise<void> {
    try {
      logger.debug("Setting config to private for user", { userId: req.user?.id }, 'config/set-private');

      const config = await this.configService.getConfigsByUserId(Number(req.user?.id));

      if (!config || !config.id) {
        throw new ValidationError('Config not found');
      }

      await this.configService.setConfigPublicStatus(config.id, false);

      logger.info("Config set to private successfully", { userId: req.user?.id, configId: config.id }, 'config/set-private');
      sendSuccessResponse(res, { id: config.id }, 'Configuration set to private successfully', req.tracingId);
    } catch (error) {
      logger.error("Failed to set config to private", {
        error: (error as any).message,
        code: (error as any).code || 'E600',
        stack: (error as any).stack,
        userId: req.user?.id
      }, 'config/set-private');

      // If it's already an AppError, use it directly
      if (error instanceof AppError) {
        sendErrorResponse(res, error, req.tracingId);
        return;
      }

      // Otherwise, wrap it in a ConfigError
      const configError = new ConfigError((error as any).message);
      sendErrorResponse(res, configError, req.tracingId);
    }
  }

}