import { QueryInterface, DataTypes } from 'sequelize';

module.exports = {
  up: async (queryInterface: QueryInterface) => {
    // Create the jusmail_configuration table
    await queryInterface.createTable('jusmail_configuration', {
      id: {
        type: DataTypes.UUID,
        primaryKey: true,
        defaultValue: DataTypes.UUIDV4
      },
      user_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      client_id: {
        type: DataTypes.INTEGER,
        allowNull: false
      },
      organization_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      domain_name: {
        type: DataTypes.STRING(255),
        allowNull: false,
        unique: true
      },
      hosted_zone_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      status: {
        type: DataTypes.ENUM(
          'CREATED',
          'ORDERED',
          'ACCEPTED',
          'REGISTERED',
          'VERIFICATION_PENDING',
          'VERIFICATION_FAILED',
          'VERIFIED',
        ),
        allowNull: false,
        defaultValue: 'CREATED'
      },
      verification_code: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      verified_at: {
        type: DataTypes.DATE,
        allowNull: true
      },
      zoho_domain_data: {
        type: DataTypes.JSON,
        allowNull: true
      },
      domain_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      dkim_id: {
        type: DataTypes.STRING(255),
        allowNull: true
      },
      dkim_public_key: {
        type: DataTypes.TEXT,
        allowNull: true
      },
      createdAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      },
      updatedAt: {
        type: DataTypes.DATE,
        allowNull: false,
        defaultValue: DataTypes.NOW
      }
    });

    // Add indexes for better query performance
    await queryInterface.addIndex('jusmail_configuration', ['domain_name'], {
      name: 'idx_jusmail_config_domain_name'
    });

    await queryInterface.addIndex('jusmail_configuration', ['status'], {
      name: 'idx_jusmail_config_status'
    });

    await queryInterface.addIndex('jusmail_configuration', ['user_id'], {
      name: 'idx_jusmail_config_user_id'
    });

    await queryInterface.addIndex('jusmail_configuration', ['client_id'], {
      name: 'idx_jusmail_config_client_id'
    });
  },

  down: async (queryInterface: QueryInterface) => {
    // Remove indexes
    await queryInterface.removeIndex('jusmail_configuration', 'idx_jusmail_config_domain_name');
    await queryInterface.removeIndex('jusmail_configuration', 'idx_jusmail_config_status');
    await queryInterface.removeIndex('jusmail_configuration', 'idx_jusmail_config_user_id');
    await queryInterface.removeIndex('jusmail_configuration', 'idx_jusmail_config_client_id');

    // Drop the table
    await queryInterface.dropTable('jusmail_configuration');
  }
};
